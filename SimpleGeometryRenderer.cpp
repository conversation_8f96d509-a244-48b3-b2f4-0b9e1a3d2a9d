#include "SimpleGeometryRenderer.h"
#include <QGraphicsLineItem>
#include <QGraphicsEllipseItem>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

SimpleGeometryRenderer::SimpleGeometryRenderer(QWidget *parent)
    : QWidget(parent)
{
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(this);
    
    // 创建场景和视图
    m_scene = new QGraphicsScene(this);
    m_view = new QGraphicsView(m_scene, this);
    
    // 设置视图属性
    m_view->setRenderHint(QPainter::Antialiasing);
    m_view->setDragMode(QGraphicsView::ScrollHandDrag);
    
    layout->addWidget(m_view);
}

void SimpleGeometryRenderer::setPoints(const std::vector<CurveSegment>& points) {
    m_points = points;
    drawPath();
}

void SimpleGeometryRenderer::drawPath() {
    // 清空场景
    m_scene->clear();
    
    if (m_points.empty()) return;
    
    // 创建路径
    QPainterPath path;
    
    for (size_t i = 0; i < m_points.size() - 1; ++i) {
        const auto& current = m_points[i];
        const auto& next = m_points[i + 1];
        
        QPointF start(current.point.x, current.point.y);
        QPointF end(next.point.x, next.point.y);
        double radius = current.radius;
        
        if (i == 0) {
            path.moveTo(start);
        }
        
        if (std::abs(radius) < 1e-6) {
            // 直线段
            path.lineTo(end);
        } else {
            // 弧线段
            // 计算圆心
            double dx = end.x() - start.x();
            double dy = end.y() - start.y();
            double chordLength = std::sqrt(dx * dx + dy * dy);
            
            if (std::abs(radius) >= chordLength / 2.0) {
                double midX = (start.x() + end.x()) / 2.0;
                double midY = (start.y() + end.y()) / 2.0;
                
                double absRadius = std::abs(radius);
                double h = std::sqrt(absRadius * absRadius - (chordLength / 2.0) * (chordLength / 2.0));
                
                double perpDx = -dy / chordLength;
                double perpDy = dx / chordLength;
                
                QPointF center;
                if (radius < 0) {
                    // 逆时针
                    center = QPointF(midX + h * perpDx, midY + h * perpDy);
                } else {
                    // 顺时针
                    center = QPointF(midX - h * perpDx, midY - h * perpDy);
                }
                
                // 计算角度
                double startAngle = std::atan2(start.y() - center.y(), start.x() - center.x()) * 180.0 / M_PI;
                double endAngle = std::atan2(end.y() - center.y(), end.x() - center.x()) * 180.0 / M_PI;
                
                double sweepAngle = endAngle - startAngle;
                if (radius < 0) {
                    // 逆时针
                    while (sweepAngle <= 0) sweepAngle += 360;
                } else {
                    // 顺时针
                    while (sweepAngle >= 0) sweepAngle -= 360;
                }
                
                // 创建边界矩形
                QRectF boundingRect(center.x() - absRadius, center.y() - absRadius, 
                                  2 * absRadius, 2 * absRadius);
                
                // 添加弧线到路径
                path.arcTo(boundingRect, startAngle, sweepAngle);
            } else {
                // 半径太小，画直线
                path.lineTo(end);
            }
        }
    }
    
    // 闭合路径（如果需要）
    if (m_points.size() > 2) {
        const auto& last = m_points.back();
        const auto& first = m_points.front();
        if (std::abs(last.point.x - first.point.x) > 1e-6 || 
            std::abs(last.point.y - first.point.y) > 1e-6) {
            path.lineTo(QPointF(first.point.x, first.point.y));
        }
    }
    
    // 添加路径到场景
    QGraphicsPathItem* pathItem = m_scene->addPath(path, QPen(Qt::black, 2));
    
    // 自动适应视图
    m_view->fitInView(m_scene->itemsBoundingRect(), Qt::KeepAspectRatio);
}
