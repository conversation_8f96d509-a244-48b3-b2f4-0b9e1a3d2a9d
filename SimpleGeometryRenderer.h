#ifndef SIMPLEGEOMETRYRENDERER_H
#define SIMPLEGEOMETRYRENDERER_H

#include <QWidget>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsPathItem>
#include <QVBoxLayout>
#include <QPainterPath>
#include "CurveData.h"

class SimpleGeometryRenderer : public QWidget {
    Q_OBJECT

public:
    explicit SimpleGeometryRenderer(QWidget *parent = nullptr);
    
    // 设置点集数据
    void setPoints(const std::vector<CurveSegment>& points);

private:
    void drawPath();
    
private:
    QGraphicsView* m_view;
    QGraphicsScene* m_scene;
    std::vector<CurveSegment> m_points;
};

#endif // SIMPLEGEOMETRYRENDERER_H
