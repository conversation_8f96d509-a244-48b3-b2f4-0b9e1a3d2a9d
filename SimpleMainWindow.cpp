#include "SimpleMainWindow.h"

SimpleMainWindow::SimpleMainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // 创建中央部件
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建布局
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
    
    // 创建控制面板
    QHBoxLayout* controlLayout = new QHBoxLayout();
    controlLayout->addWidget(new QLabel("选择数据:"));
    
    m_dataSelector = new QComboBox();
    m_dataSelector->addItem("曲线组1", 0);
    m_dataSelector->addItem("曲线组2", 1);
    controlLayout->addWidget(m_dataSelector);
    controlLayout->addStretch();
    
    mainLayout->addLayout(controlLayout);
    
    // 创建渲染器
    m_renderer = new SimpleGeometryRenderer(this);
    mainLayout->addWidget(m_renderer);
    
    // 连接信号
    connect(m_dataSelector, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SimpleMainWindow::onDataChanged);
    
    // 设置窗口属性
    setWindowTitle("简单几何图形渲染器");
    resize(800, 600);
    
    // 默认加载第二组数据
    m_dataSelector->setCurrentIndex(1);
    onDataChanged();
}

void SimpleMainWindow::onDataChanged() {
    int index = m_dataSelector->currentData().toInt();
    
    if (index == 0) {
        m_renderer->setPoints(CurveData::getCurves1());
    } else {
        m_renderer->setPoints(CurveData::getCurves2());
    }
}
