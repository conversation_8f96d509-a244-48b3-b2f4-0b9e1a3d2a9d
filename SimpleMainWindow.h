#ifndef SIMPLEMAINWINDOW_H
#define SIMPLEMAINWINDOW_H

#include <QMainWindow>
#include <QComboBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include "SimpleGeometryRenderer.h"

class SimpleMainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit SimpleMainWindow(QWidget *parent = nullptr);

private slots:
    void onDataChanged();

private:
    SimpleGeometryRenderer* m_renderer;
    QComboBox* m_dataSelector;
};

#endif // SIMPLEMAINWINDOW_H
