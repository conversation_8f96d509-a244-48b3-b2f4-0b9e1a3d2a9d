{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/PyCharmMiscProject/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/PyCharmMiscProject/build", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/PyCharmMiscProject", "CMAKE_EXECUTABLE": "C:/Users/<USER>/scoop/apps/cmake/3.31.0/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Users/<USER>/PyCharmMiscProject/CMakeLists.txt", "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/3.31.0/CMakeSystem.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake", "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/3.31.0/CMakeCXXCompiler.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeGenericSystem.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Windows.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeCXXInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Compiler/MSVC-CXX.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Compiler/MSVC.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Windows-MSVC.cmake", "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/3.31.0/CMakeRCCompiler.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeRCInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC-CXX.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtInstallPaths.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Targets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtFeature.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckFlagCommonConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindThreads.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckLibraryExists.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckIncludeFileCXX.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateVersionlessAliasTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateVersionlessAliasTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/GNUInstallDirs.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindVulkan.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginConfig.cmake", "C:/Users/<USER>/scoop/apps/cmake/3.31.0/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-debug.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake", "C:/Qt/6.8.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake"], "CMAKE_SOURCE_DIR": "C:/Users/<USER>/PyCharmMiscProject", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["C:/Users/<USER>/PyCharmMiscProject/CurveData.h", "MU", "EWIEGA46WW/moc_CurveData.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/include", "INCLUDE_DIR_Debug": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/include_Release", "MOC_COMPILATION_FILE": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "C:/Users/<USER>/PyCharmMiscProject/build/SimpleGeometry_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["C:/Qt/6.8.0/msvc2022_64/include/QtCore", "C:/Qt/6.8.0/msvc2022_64/include", "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc", "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets", "C:/Qt/6.8.0/msvc2022_64/include/QtGui"], "MOC_INCLUDES_MinSizeRel": ["C:/Qt/6.8.0/msvc2022_64/include/QtCore", "C:/Qt/6.8.0/msvc2022_64/include", "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc", "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets", "C:/Qt/6.8.0/msvc2022_64/include/QtGui"], "MOC_INCLUDES_RelWithDebInfo": ["C:/Qt/6.8.0/msvc2022_64/include/QtCore", "C:/Qt/6.8.0/msvc2022_64/include", "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc", "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets", "C:/Qt/6.8.0/msvc2022_64/include/QtGui"], "MOC_INCLUDES_Release": ["C:/Qt/6.8.0/msvc2022_64/include/QtCore", "C:/Qt/6.8.0/msvc2022_64/include", "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc", "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets", "C:/Qt/6.8.0/msvc2022_64/include/QtGui"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 14, "PARSE_CACHE_FILE": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "", "QT_MOC_EXECUTABLE_Debug": "C:/Qt/6.8.0/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_MinSizeRel": "C:/Qt/6.8.0/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_RelWithDebInfo": "C:/Qt/6.8.0/msvc2022_64/bin/moc.exe", "QT_MOC_EXECUTABLE_Release": "C:/Qt/6.8.0/msvc2022_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_UIC_EXECUTABLE_Debug": "C:/Qt/6.8.0/msvc2022_64/bin/uic.exe", "QT_UIC_EXECUTABLE_MinSizeRel": "C:/Qt/6.8.0/msvc2022_64/bin/uic.exe", "QT_UIC_EXECUTABLE_RelWithDebInfo": "C:/Qt/6.8.0/msvc2022_64/bin/uic.exe", "QT_UIC_EXECUTABLE_Release": "C:/Qt/6.8.0/msvc2022_64/bin/uic.exe", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Users/<USER>/PyCharmMiscProject/build/CMakeFiles/SimpleGeometry_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["C:/Users/<USER>/PyCharmMiscProject/simple_geometry.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}