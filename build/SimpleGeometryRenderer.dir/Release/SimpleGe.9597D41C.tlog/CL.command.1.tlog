^C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLE_MAIN.CPP
/c /IC:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\INCLUDE_RELEASE /I"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\INCLUDE" /nologo /W1 /WX- /diagnostics:column /O2 /Ob2 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D NDEBUG /D QT_CORE_LIB /D QT_NO_DEBUG /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_WIDGETS_LIB /D QT_GUI_LIB /D "CMAKE_INTDIR=\"Release\"" /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /permissive- /Fo"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\\" /Fd"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.8.0/msvc2022_64/include" /external:I "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtGui" -Zc:__cplusplus -utf-8 C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLE_MAIN.CPP
^C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLEMAINWINDOW.CPP
/c /IC:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\INCLUDE_RELEASE /I"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\INCLUDE" /nologo /W1 /WX- /diagnostics:column /O2 /Ob2 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D NDEBUG /D QT_CORE_LIB /D QT_NO_DEBUG /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_WIDGETS_LIB /D QT_GUI_LIB /D "CMAKE_INTDIR=\"Release\"" /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /permissive- /Fo"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\\" /Fd"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.8.0/msvc2022_64/include" /external:I "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtGui" -Zc:__cplusplus -utf-8 C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLEMAINWINDOW.CPP
^C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLEGEOMETRYRENDERER.CPP
/c /IC:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\INCLUDE_RELEASE /I"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\INCLUDE" /nologo /W1 /WX- /diagnostics:column /O2 /Ob2 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D NDEBUG /D QT_CORE_LIB /D QT_NO_DEBUG /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_WIDGETS_LIB /D QT_GUI_LIB /D "CMAKE_INTDIR=\"Release\"" /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /permissive- /Fo"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\\" /Fd"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.8.0/msvc2022_64/include" /external:I "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtGui" -Zc:__cplusplus -utf-8 C:\USERS\<USER>\PYCHARMMISCPROJECT\SIMPLEGEOMETRYRENDERER.CPP
^C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\MOCS_COMPILATION_RELEASE.CPP
/c /IC:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\INCLUDE_RELEASE /I"C:\USERS\<USER>\SCOOP\APPS\VCPKG\CURRENT\INSTALLED\X64-WINDOWS\INCLUDE" /nologo /W1 /WX- /diagnostics:column /O2 /Ob2 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D NDEBUG /D QT_CORE_LIB /D QT_NO_DEBUG /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_WIDGETS_LIB /D QT_GUI_LIB /D "CMAKE_INTDIR=\"Release\"" /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /permissive- /Fo"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\\" /Fd"SIMPLEGEOMETRYRENDERER.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.8.0/msvc2022_64/include" /external:I "C:/Qt/6.8.0/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.8.0/msvc2022_64/include/QtGui" -Zc:__cplusplus -utf-8 C:\USERS\<USER>\PYCHARMMISCPROJECT\BUILD\SIMPLEGEOMETRYRENDERER_AUTOGEN\MOCS_COMPILATION_RELEASE.CPP
