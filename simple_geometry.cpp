#include <QApplication>
#include <QMainWindow>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsPathItem>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QComboBox>
#include <QLabel>
#include <QPainterPath>
#include <cmath>
#include "CurveData.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

class SimpleRenderer : public QWidget {
    Q_OBJECT

public:
    SimpleRenderer(QWidget *parent = nullptr) : QWidget(parent) {
        QVBoxLayout* layout = new QVBoxLayout(this);
        
        // 控制面板
        QHBoxLayout* controlLayout = new QHBoxLayout();
        controlLayout->addWidget(new QLabel("选择数据:"));
        
        m_selector = new QComboBox();
        m_selector->addItem("曲线组1", 0);
        m_selector->addItem("曲线组2", 1);
        controlLayout->addWidget(m_selector);
        controlLayout->addStretch();
        
        layout->addLayout(controlLayout);
        
        // 图形视图
        m_scene = new QGraphicsScene(this);
        m_view = new QGraphicsView(m_scene, this);
        m_view->setRenderHint(QPainter::Antialiasing);
        m_view->setDragMode(QGraphicsView::ScrollHandDrag);
        
        layout->addWidget(m_view);
        
        // 连接信号
        connect(m_selector, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &SimpleRenderer::drawCurves);
        
        // 默认显示第二组数据
        m_selector->setCurrentIndex(1);
        drawCurves();
    }

private slots:
    void drawCurves() {
        m_scene->clear();
        
        int index = m_selector->currentData().toInt();
        std::vector<CurveSegment> points;
        
        if (index == 0) {
            points = CurveData::getCurves1();
        } else {
            points = CurveData::getCurves2();
        }
        
        if (points.empty()) return;
        
        QPainterPath path;
        
        for (size_t i = 0; i < points.size() - 1; ++i) {
            const auto& current = points[i];
            const auto& next = points[i + 1];
            
            QPointF start(current.point.x, current.point.y);
            QPointF end(next.point.x, next.point.y);
            double radius = current.radius;
            
            if (i == 0) {
                path.moveTo(start);
            }
            
            if (std::abs(radius) < 1e-6) {
                // 直线
                path.lineTo(end);
            } else {
                // 弧线
                double dx = end.x() - start.x();
                double dy = end.y() - start.y();
                double chordLength = std::sqrt(dx * dx + dy * dy);
                
                if (std::abs(radius) >= chordLength / 2.0) {
                    // 计算圆心
                    double midX = (start.x() + end.x()) / 2.0;
                    double midY = (start.y() + end.y()) / 2.0;
                    
                    double absRadius = std::abs(radius);
                    double h = std::sqrt(absRadius * absRadius - (chordLength / 2.0) * (chordLength / 2.0));
                    
                    double perpDx = -dy / chordLength;
                    double perpDy = dx / chordLength;
                    
                    QPointF center;
                    if (radius < 0) {
                        center = QPointF(midX + h * perpDx, midY + h * perpDy);
                    } else {
                        center = QPointF(midX - h * perpDx, midY - h * perpDy);
                    }
                    
                    // 计算角度
                    double startAngle = std::atan2(start.y() - center.y(), start.x() - center.x()) * 180.0 / M_PI;
                    double endAngle = std::atan2(end.y() - center.y(), end.x() - center.x()) * 180.0 / M_PI;
                    
                    double sweepAngle = endAngle - startAngle;
                    if (radius < 0) {
                        while (sweepAngle <= 0) sweepAngle += 360;
                    } else {
                        while (sweepAngle >= 0) sweepAngle -= 360;
                    }
                    
                    QRectF boundingRect(center.x() - absRadius, center.y() - absRadius, 
                                      2 * absRadius, 2 * absRadius);
                    
                    path.arcTo(boundingRect, startAngle, sweepAngle);
                } else {
                    path.lineTo(end);
                }
            }
        }
        
        // 添加到场景
        m_scene->addPath(path, QPen(Qt::black, 2));
        m_view->fitInView(m_scene->itemsBoundingRect(), Qt::KeepAspectRatio);
    }

private:
    QGraphicsView* m_view;
    QGraphicsScene* m_scene;
    QComboBox* m_selector;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    QMainWindow window;
    SimpleRenderer* renderer = new SimpleRenderer();
    window.setCentralWidget(renderer);
    window.setWindowTitle("简单几何图形渲染器");
    window.resize(800, 600);
    window.show();
    
    return app.exec();
}

#include "simple_geometry.moc"
